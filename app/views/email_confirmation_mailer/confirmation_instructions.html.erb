<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= I18n.t('email_confirmation_mailer.confirmation_instructions.subject', default: 'Potvrzení emailové adresy') %></title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f8f9fa;
      }
      .container {
        background-color: #ffffff;
        padding: 40px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .logo {
        color: #22C55E;
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      h1 {
        color: #1f2937;
        font-size: 28px;
        margin-bottom: 20px;
        text-align: center;
      }
      .content {
        margin-bottom: 30px;
      }
      .confirm-button {
        display: inline-block;
        background-color: #059669;
        color: #ffffff;
        text-decoration: none;
        padding: 14px 28px;
        border-radius: 6px;
        font-weight: 600;
        text-align: center;
        margin: 20px auto;
        display: block;
        width: fit-content;
      }
      .confirm-button:hover {
        background-color: #047857;
      }
      .welcome-notice {
        background-color: #dcfce7;
        border-left: 4px solid #22c55e;
        padding: 15px;
        margin: 20px 0;
        border-radius: 4px;
      }
      .footer {
        color: #6b7280;
        font-size: 14px;
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
      }
      .alternative-link {
        color: #6b7280;
        font-size: 12px;
        word-break: break-all;
        margin-top: 15px;
        padding: 10px;
        background-color: #f9fafb;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <div class="logo">Týmbox</div>
      </div>
      
      <h1><%= I18n.t('email_confirmation_mailer.confirmation_instructions.welcome', default: 'Vítejte v Týmboxu!') %></h1>
      
      <div class="content">
        <p><%= I18n.t('email_confirmation_mailer.confirmation_instructions.greeting', default: 'Dobrý den') %> <%= @user.email %>,</p>
        
        <p><%= I18n.t('email_confirmation_mailer.confirmation_instructions.intro', 
          default: 'děkujeme za registraci do Týmboxu! Pro dokončení registrace prosím potvrďte svůj email.') %></p>
        
        <div class="welcome-notice">
          <strong><%= I18n.t('email_confirmation_mailer.confirmation_instructions.almost_there', default: 'Už to skoro máte!') %></strong>
          <p style="margin: 10px 0;"><%= I18n.t('email_confirmation_mailer.confirmation_instructions.features_text', 
            default: 'Po potvrzení emailu budete mít přístup ke všem funkcím Týmboxu včetně evidence času, správy týmu a reportování.') %></p>
        </div>
        
        <p><%= I18n.t('email_confirmation_mailer.confirmation_instructions.confirm_email', 
          default: 'Pro aktivaci účtu potvrďte svůj email:') %></p>
        
        <a href="<%= @confirmation_url %>" class="confirm-button">
          <%= I18n.t('email_confirmation_mailer.confirmation_instructions.confirm_account', default: 'Aktivovat účet') %>
        </a>
        
        <p><%= I18n.t('email_confirmation_mailer.confirmation_instructions.expiry_text', 
          default: 'Tento potvrzovací odkaz vyprší za 24 hodin. Pokud budete potřebovat nový potvrzovací email, můžete si ho vyžádat ze stránky přihlášení.') %></p>
        
        <p><%= I18n.t('email_confirmation_mailer.confirmation_instructions.no_request', 
          default: 'Pokud jste si u nás účet nevytvořili, můžete tento email bezpečně ignorovat.') %></p>
      </div>
      
      <div class="alternative-link">
        <p><%= I18n.t('email_confirmation_mailer.confirmation_instructions.link_trouble', 
          default: 'Máte problém s tlačítkem výše? Zkopírujte a vložte tento odkaz do vašeho prohlížeče:') %></p>
        <p><%= @confirmation_url %></p>
      </div>
      
      <div class="footer">
        <%= render 'shared/email_footer' %>
      </div>
    </div>
  </body>
</html>